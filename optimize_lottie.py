#!/usr/bin/env python3
"""
Быстрый скрипт оптимизации Lottie JSON без заморочек
"""

import json
import os
import sys

def optimize_lottie(input_file, output_file=None):
    """Оптимизирует Lottie JSON файл"""
    
    if not output_file:
        name, ext = os.path.splitext(input_file)
        output_file = f"{name}_optimized{ext}"
    
    # Читаем файл
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        print(f"❌ Ошибка чтения {input_file}: {e}")
        return False
    
    original_size = os.path.getsize(input_file)
    print(f"📁 Исходный размер: {original_size:,} байт")
    
    # Применяем оптимизации
    optimizations = []
    
    # 1. Округляем числа до 2 знаков
    data = round_numbers(data, 2)
    optimizations.append("Округлены числа")
    
    # 2. Удаляем пустые свойства
    data = remove_empty_props(data)
    optimizations.append("Удалены пустые свойства")
    
    # 3. Удаляем метаданные
    removed_meta = []
    for key in ['meta', 'generator', 'description', 'author', 'keywords', 'comment']:
        if key in data:
            del data[key]
            removed_meta.append(key)
    if removed_meta:
        optimizations.append(f"Удалены метаданные: {', '.join(removed_meta)}")
    
    # 4. Оптимизируем ассеты (удаляем неиспользуемые)
    if 'assets' in data:
        original_assets = len(data['assets'])
        data['assets'] = optimize_assets(data)
        if len(data['assets']) < original_assets:
            optimizations.append(f"Удалено {original_assets - len(data['assets'])} неиспользуемых ассетов")
    
    # Сохраняем оптимизированный файл (без пробелов и отступов)
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, separators=(',', ':'), ensure_ascii=False)
    except Exception as e:
        print(f"❌ Ошибка записи {output_file}: {e}")
        return False
    
    # Показываем результат
    optimized_size = os.path.getsize(output_file)
    reduction = ((original_size - optimized_size) / original_size) * 100
    
    print(f"✅ Оптимизация завершена!")
    print(f"📁 Новый размер: {optimized_size:,} байт")
    print(f"📉 Сжатие: {reduction:.1f}%")
    print(f"💾 Сохранено в: {output_file}")
    print(f"🔧 Применено оптимизаций: {len(optimizations)}")
    for opt in optimizations:
        print(f"   • {opt}")
    
    return True

def round_numbers(obj, precision=2):
    """Рекурсивно округляет числа"""
    if isinstance(obj, dict):
        return {k: round_numbers(v, precision) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [round_numbers(item, precision) for item in obj]
    elif isinstance(obj, float):
        return round(obj, precision)
    return obj

def remove_empty_props(obj):
    """Удаляет пустые свойства"""
    if isinstance(obj, dict):
        cleaned = {}
        for k, v in obj.items():
            cleaned_v = remove_empty_props(v)
            if cleaned_v is not None and cleaned_v != [] and cleaned_v != {}:
                cleaned[k] = cleaned_v
        return cleaned
    elif isinstance(obj, list):
        return [remove_empty_props(item) for item in obj if item is not None]
    return obj

def optimize_assets(data):
    """Простая оптимизация ассетов - удаляет те, что не используются"""
    if 'assets' not in data or not isinstance(data['assets'], list):
        return data.get('assets', [])
    
    # Собираем все используемые ID
    used_ids = set()
    find_used_ids(data, used_ids)
    
    # Оставляем только используемые ассеты
    optimized_assets = []
    for asset in data['assets']:
        asset_id = asset.get('id')
        if not asset_id or asset_id in used_ids:
            optimized_assets.append(asset)
    
    return optimized_assets

def find_used_ids(obj, used_ids):
    """Рекурсивно находит используемые ID ассетов"""
    if isinstance(obj, dict):
        if 'refId' in obj:
            used_ids.add(obj['refId'])
        for value in obj.values():
            find_used_ids(value, used_ids)
    elif isinstance(obj, list):
        for item in obj:
            find_used_ids(item, used_ids)

def main():
    if len(sys.argv) < 2:
        print("Использование: python optimize_lottie.py <input.json> [output.json]")
        print("Пример: python optimize_lottie.py 1.json")
        return
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    if not os.path.exists(input_file):
        print(f"❌ Файл {input_file} не найден")
        return
    
    print(f"🚀 Оптимизируем {input_file}...")
    optimize_lottie(input_file, output_file)

if __name__ == '__main__':
    main()
