#!/usr/bin/env python3
"""
Скрипт для оптимизации JSON файлов Lottie анимаций
Быстрая и простая оптимизация без лишних заморочек
"""

import json
import os
import sys
import argparse
from pathlib import Path
import shutil
from typing import Dict, Any, List

class LottieOptimizer:
    def __init__(self):
        self.optimizations_applied = []
        
    def optimize_file(self, input_path: str, output_path: str = None) -> Dict[str, Any]:
        """Оптимизирует один Lottie JSON файл"""
        if not output_path:
            output_path = input_path.replace('.json', '_optimized.json')
            
        try:
            with open(input_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            return {'error': f'Ошибка чтения файла: {e}'}
            
        original_size = os.path.getsize(input_path)
        self.optimizations_applied = []
        
        # Применяем оптимизации
        data = self._remove_unused_assets(data)
        data = self._round_numbers(data)
        data = self._remove_empty_properties(data)
        data = self._optimize_keyframes(data)
        data = self._remove_metadata(data)
        
        # Сохраняем оптимизированный файл
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, separators=(',', ':'), ensure_ascii=False)
        except Exception as e:
            return {'error': f'Ошибка записи файла: {e}'}
            
        optimized_size = os.path.getsize(output_path)
        reduction = ((original_size - optimized_size) / original_size) * 100
        
        return {
            'input_file': input_path,
            'output_file': output_path,
            'original_size': original_size,
            'optimized_size': optimized_size,
            'size_reduction': f'{reduction:.1f}%',
            'optimizations': self.optimizations_applied
        }
    
    def _remove_unused_assets(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Удаляет неиспользуемые ассеты"""
        if 'assets' in data and isinstance(data['assets'], list):
            # Простая проверка - если ассет не используется, удаляем
            used_assets = set()
            self._find_used_assets(data, used_assets)
            
            original_count = len(data['assets'])
            data['assets'] = [asset for asset in data['assets'] 
                            if asset.get('id') in used_assets or not asset.get('id')]
            
            if len(data['assets']) < original_count:
                self.optimizations_applied.append(f'Удалено {original_count - len(data["assets"])} неиспользуемых ассетов')
                
        return data
    
    def _find_used_assets(self, obj: Any, used_assets: set):
        """Рекурсивно находит используемые ассеты"""
        if isinstance(obj, dict):
            if 'refId' in obj:
                used_assets.add(obj['refId'])
            for value in obj.values():
                self._find_used_assets(value, used_assets)
        elif isinstance(obj, list):
            for item in obj:
                self._find_used_assets(item, used_assets)
    
    def _round_numbers(self, data: Dict[str, Any], precision: int = 3) -> Dict[str, Any]:
        """Округляет числа для уменьшения размера"""
        def round_recursive(obj):
            if isinstance(obj, dict):
                return {k: round_recursive(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [round_recursive(item) for item in obj]
            elif isinstance(obj, float):
                return round(obj, precision)
            return obj
        
        self.optimizations_applied.append(f'Округлены числа до {precision} знаков')
        return round_recursive(data)
    
    def _remove_empty_properties(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Удаляет пустые свойства"""
        def clean_recursive(obj):
            if isinstance(obj, dict):
                cleaned = {}
                for k, v in obj.items():
                    cleaned_v = clean_recursive(v)
                    if cleaned_v is not None and cleaned_v != [] and cleaned_v != {}:
                        cleaned[k] = cleaned_v
                return cleaned
            elif isinstance(obj, list):
                return [clean_recursive(item) for item in obj if item is not None]
            return obj
        
        self.optimizations_applied.append('Удалены пустые свойства')
        return clean_recursive(data)
    
    def _optimize_keyframes(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Оптимизирует ключевые кадры"""
        def optimize_keyframes_recursive(obj):
            if isinstance(obj, dict):
                # Удаляем дублирующиеся ключевые кадры
                if 'k' in obj and isinstance(obj['k'], list):
                    original_length = len(obj['k'])
                    obj['k'] = self._remove_duplicate_keyframes(obj['k'])
                    if len(obj['k']) < original_length:
                        self.optimizations_applied.append(f'Удалено {original_length - len(obj["k"])} дублирующихся кадров')
                
                return {k: optimize_keyframes_recursive(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [optimize_keyframes_recursive(item) for item in obj]
            return obj
        
        return optimize_keyframes_recursive(data)
    
    def _remove_duplicate_keyframes(self, keyframes: List[Dict]) -> List[Dict]:
        """Удаляет дублирующиеся ключевые кадры"""
        if len(keyframes) <= 1:
            return keyframes
            
        unique_keyframes = [keyframes[0]]
        for i in range(1, len(keyframes)):
            current = keyframes[i]
            previous = keyframes[i-1]
            
            # Сравниваем значения (упрощенная проверка)
            if current.get('s') != previous.get('s') or current.get('e') != previous.get('e'):
                unique_keyframes.append(current)
                
        return unique_keyframes
    
    def _remove_metadata(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Удаляет ненужные метаданные"""
        metadata_keys = ['meta', 'generator', 'description', 'author', 'keywords']
        removed_keys = []
        
        for key in metadata_keys:
            if key in data:
                del data[key]
                removed_keys.append(key)
        
        if removed_keys:
            self.optimizations_applied.append(f'Удалены метаданные: {", ".join(removed_keys)}')
            
        return data

def main():
    parser = argparse.ArgumentParser(description='Оптимизация Lottie JSON файлов')
    parser.add_argument('input', help='Путь к JSON файлу или папке с JSON файлами')
    parser.add_argument('-o', '--output', help='Путь для сохранения (опционально)')
    parser.add_argument('-r', '--recursive', action='store_true', help='Рекурсивный поиск в подпапках')
    parser.add_argument('--backup', action='store_true', help='Создать резервную копию оригинала')
    
    args = parser.parse_args()
    
    optimizer = LottieOptimizer()
    input_path = Path(args.input)
    
    if input_path.is_file() and input_path.suffix.lower() == '.json':
        # Оптимизируем один файл
        if args.backup:
            shutil.copy2(str(input_path), str(input_path).replace('.json', '_backup.json'))
            
        result = optimizer.optimize_file(str(input_path), args.output)
        print_result(result)
        
    elif input_path.is_dir():
        # Оптимизируем все JSON файлы в папке
        pattern = '**/*.json' if args.recursive else '*.json'
        json_files = list(input_path.glob(pattern))
        
        if not json_files:
            print(f"JSON файлы не найдены в {input_path}")
            return
            
        print(f"Найдено {len(json_files)} JSON файлов")
        
        for json_file in json_files:
            if args.backup:
                shutil.copy2(str(json_file), str(json_file).replace('.json', '_backup.json'))
                
            result = optimizer.optimize_file(str(json_file))
            print_result(result)
            
    else:
        print(f"Ошибка: {input_path} не является JSON файлом или папкой")

def print_result(result: Dict[str, Any]):
    """Выводит результат оптимизации"""
    if 'error' in result:
        print(f"❌ {result['error']}")
        return
        
    print(f"\n✅ {result['input_file']}")
    print(f"   Размер: {result['original_size']} → {result['optimized_size']} байт")
    print(f"   Сжатие: {result['size_reduction']}")
    print(f"   Сохранено в: {result['output_file']}")
    
    if result['optimizations']:
        print("   Применённые оптимизации:")
        for opt in result['optimizations']:
            print(f"   • {opt}")

if __name__ == '__main__':
    main()
