# Lottie JSON Optimizer

Быстрый и простой скрипт для оптимизации JSON файлов Lottie анимаций без лишних заморочек.

## Что делает

- ✅ Удаляет ненужные метаданные (meta, generator, description, author, keywords, comment)
- ✅ Сжимает JSON (убирает пробелы и отступы)
- ✅ Очищает пустые свойства
- ✅ Показывает размер до/после и процент сжатия

## Как использовать

### Вариант 1: Batch файл (самый простой)
```bash
optimize.bat 1.json
```

### Вариант 2: PowerShell скрипт
```powershell
.\optimize_lottie.ps1 -InputFile 1.json
```

### Вариант 3: С указанием выходного файла
```powershell
.\optimize_lottie.ps1 -InputFile 1.json -OutputFile optimized.json
```

## Результат

Скрипт создаст новый файл с суффиксом `_optimized` (например, `1_optimized.json`) и покажет:
- Исходный размер файла
- Новый размер файла  
- Процент сжатия
- Список примененных оптимизаций

## Пример вывода

```
🚀 Оптимизируем 1.json...
📁 Исходный размер: 3,536,750 байт
✅ Оптимизация завершена!
📁 Новый размер: 3,534,465 байт
📉 Сжатие: 0.1%
💾 Сохранено в: .\1_optimized.json
🔧 Применено оптимизаций: 1
   • Очищены пустые свойства
```

## Файлы в проекте

- `optimize.bat` - Простой batch файл для запуска
- `optimize_lottie.ps1` - Основной PowerShell скрипт
- `optimize_lottie.py` - Python версия (требует установки Python)
- `lottie_optimizer.py` - Расширенная Python версия с дополнительными функциями

## Требования

- Windows с PowerShell (встроен в Windows)
- Для Python версий: Python 3.6+

## Быстрый старт

1. Поместите ваш JSON файл в папку со скриптами
2. Запустите: `optimize.bat ваш_файл.json`
3. Получите оптимизированный файл с суффиксом `_optimized`

Готово! 🎉
