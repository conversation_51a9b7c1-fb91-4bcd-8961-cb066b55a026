# Оптимизация Lottie JSON с сохранением изображений
param(
    [Parameter(Mandatory=$true)]
    [string]$InputFile,
    
    [Parameter(Mandatory=$false)]
    [string]$OutputFile = "",
    
    [Parameter(Mandatory=$false)]
    [int]$Quality = 80
)

function Optimize-LottieImages {
    param($InputFile, $OutputFile, $Quality)
    
    # Проверяем существование файла
    if (-not (Test-Path $InputFile)) {
        Write-Host "❌ Файл $InputFile не найден" -ForegroundColor Red
        return $false
    }
    
    # Определяем выходной файл
    if ([string]::IsNullOrEmpty($OutputFile)) {
        $name = [System.IO.Path]::GetFileNameWithoutExtension($InputFile)
        $ext = [System.IO.Path]::GetExtension($InputFile)
        $dir = Split-Path $InputFile -Parent
        if ([string]::IsNullOrEmpty($dir)) { $dir = "." }
        $OutputFile = "$dir\$name`_compressed$ext"
    }
    
    Write-Host "🖼️  СЖАТИЕ ИЗОБРАЖЕНИЙ в $InputFile..." -ForegroundColor Cyan
    Write-Host "   Качество: $Quality%" -ForegroundColor Gray
    
    try {
        # Читаем JSON как текст
        $jsonContent = Get-Content $InputFile -Raw -Encoding UTF8
        $originalSize = (Get-Item $InputFile).Length
        Write-Host "📁 Исходный размер: $($originalSize.ToString('N0')) байт" -ForegroundColor Yellow
        
        $optimizations = @()
        $optimizedContent = $jsonContent
        
        # Находим все base64 изображения
        $base64Pattern = 'data:image/([^;]+);base64,([A-Za-z0-9+/=]+)'
        $matches = [regex]::Matches($optimizedContent, $base64Pattern)
        
        if ($matches.Count -gt 0) {
            Write-Host "   🔍 Найдено $($matches.Count) изображений для сжатия" -ForegroundColor Gray
            
            $totalSaved = 0
            $imageIndex = 1
            
            foreach ($match in $matches) {
                $fullMatch = $match.Value
                $imageType = $match.Groups[1].Value
                $base64Data = $match.Groups[2].Value
                
                Write-Host "   📷 Сжимаем изображение $imageIndex/$($matches.Count) (тип: $imageType)..." -ForegroundColor Gray
                
                try {
                    # Декодируем base64
                    $imageBytes = [Convert]::FromBase64String($base64Data)
                    $originalImageSize = $imageBytes.Length
                    
                    # Создаем временный файл
                    $tempFile = [System.IO.Path]::GetTempFileName()
                    $tempImageFile = "$tempFile.$imageType"
                    $tempCompressedFile = "$tempFile.compressed.$imageType"
                    
                    # Сохраняем оригинальное изображение
                    [System.IO.File]::WriteAllBytes($tempImageFile, $imageBytes)
                    
                    # Сжимаем изображение с помощью .NET
                    $compressedBytes = Compress-Image $tempImageFile $tempCompressedFile $Quality $imageType
                    
                    if ($compressedBytes -and $compressedBytes.Length -lt $originalImageSize) {
                        # Конвертируем обратно в base64
                        $compressedBase64 = [Convert]::ToBase64String($compressedBytes)
                        $newDataUri = "data:image/$imageType;base64,$compressedBase64"
                        
                        # Заменяем в JSON
                        $optimizedContent = $optimizedContent.Replace($fullMatch, $newDataUri)
                        
                        $saved = $originalImageSize - $compressedBytes.Length
                        $totalSaved += $saved
                        $compressionPercent = [math]::Round(($saved / $originalImageSize) * 100, 1)
                        
                        Write-Host "     ✅ $originalImageSize → $($compressedBytes.Length) байт (-$compressionPercent%)" -ForegroundColor Green
                    } else {
                        Write-Host "     ⚠️  Сжатие не дало результата, оставляем оригинал" -ForegroundColor Yellow
                    }
                    
                    # Удаляем временные файлы
                    if (Test-Path $tempFile) { Remove-Item $tempFile -Force }
                    if (Test-Path $tempImageFile) { Remove-Item $tempImageFile -Force }
                    if (Test-Path $tempCompressedFile) { Remove-Item $tempCompressedFile -Force }
                    
                } catch {
                    Write-Host "     ❌ Ошибка сжатия: $($_.Exception.Message)" -ForegroundColor Red
                }
                
                $imageIndex++
            }
            
            if ($totalSaved -gt 0) {
                $optimizations += "Сжато $($matches.Count) изображений, сэкономлено $($totalSaved.ToString('N0')) байт"
            } else {
                $optimizations += "Изображения уже оптимальны"
            }
        }
        
        # Минимальное сжатие JSON (убираем лишние пробелы)
        $optimizedContent = $optimizedContent -replace '\s+', ' '
        $optimizedContent = $optimizedContent -replace ':\s+', ':'
        $optimizedContent = $optimizedContent -replace ',\s+', ','
        $optimizedContent = $optimizedContent -replace '{\s+', '{'
        $optimizedContent = $optimizedContent -replace '\s+}', '}'
        $optimizedContent = $optimizedContent -replace '\[\s+', '['
        $optimizedContent = $optimizedContent -replace '\s+]', ']'
        
        $optimizations += "Минимальное сжатие JSON"
        
        # Сохраняем результат
        $optimizedContent | Out-File -FilePath $OutputFile -Encoding UTF8 -NoNewline
        
        $optimizedSize = (Get-Item $OutputFile).Length
        $reduction = [math]::Round((($originalSize - $optimizedSize) / $originalSize) * 100, 1)
        
        Write-Host "✅ СЖАТИЕ ИЗОБРАЖЕНИЙ завершено!" -ForegroundColor Green
        Write-Host "📁 Новый размер: $($optimizedSize.ToString('N0')) байт" -ForegroundColor Yellow
        Write-Host "📉 Сжатие: $reduction%" -ForegroundColor Green
        Write-Host "💾 Сохранено в: $OutputFile" -ForegroundColor Cyan
        Write-Host "🔧 Применено оптимизаций: $($optimizations.Count)" -ForegroundColor Magenta
        
        foreach ($opt in $optimizations) {
            Write-Host "   • $opt" -ForegroundColor Gray
        }
        
        Write-Host ""
        Write-Host "✅ Все изображения сохранены и сжаты!" -ForegroundColor Green
        
        return $true
        
    } catch {
        Write-Host "❌ Ошибка: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Compress-Image($inputPath, $outputPath, $quality, $imageType) {
    try {
        # Загружаем .NET сборки для работы с изображениями
        Add-Type -AssemblyName System.Drawing
        
        # Загружаем изображение
        $image = [System.Drawing.Image]::FromFile($inputPath)
        
        # Настройки сжатия
        $encoder = [System.Drawing.Imaging.ImageCodecInfo]::GetImageEncoders() | Where-Object { $_.MimeType -eq "image/$imageType" }
        if (-not $encoder) {
            $encoder = [System.Drawing.Imaging.ImageCodecInfo]::GetImageEncoders() | Where-Object { $_.MimeType -eq "image/jpeg" }
        }
        
        $encoderParams = New-Object System.Drawing.Imaging.EncoderParameters(1)
        $encoderParams.Param[0] = New-Object System.Drawing.Imaging.EncoderParameter([System.Drawing.Imaging.Encoder]::Quality, $quality)
        
        # Сохраняем сжатое изображение
        $image.Save($outputPath, $encoder, $encoderParams)
        $image.Dispose()
        
        # Читаем сжатые байты
        return [System.IO.File]::ReadAllBytes($outputPath)
        
    } catch {
        Write-Host "Ошибка сжатия изображения: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# Основная логика
if ([string]::IsNullOrEmpty($InputFile)) {
    Write-Host "Использование: .\optimize_images.ps1 -InputFile <input.json> [-OutputFile <output.json>] [-Quality <1-100>]" -ForegroundColor Yellow
    Write-Host "Пример: .\optimize_images.ps1 -InputFile 1.json -Quality 70" -ForegroundColor Yellow
    exit 1
}

$result = Optimize-LottieImages -InputFile $InputFile -OutputFile $OutputFile -Quality $Quality

if ($result) {
    Write-Host "🎉 СЖАТИЕ ИЗОБРАЖЕНИЙ завершено!" -ForegroundColor Green
} else {
    Write-Host "💥 Что-то пошло не так." -ForegroundColor Red
    exit 1
}
