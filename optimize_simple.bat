@echo off
echo Lottie JSON Optimizer - Simple Version
echo ======================================
echo.

if "%1"=="" (
    echo Usage: optimize_simple.bat filename.json
    echo Example: optimize_simple.bat 1.json
    pause
    exit /b 1
)

if not exist "%1" (
    echo File %1 not found
    pause
    exit /b 1
)

echo Choose optimization mode:
echo 1 - Safe (keeps all images, 0.1%% compression, 100%% works)
echo 2 - Working (transparent pixels, 99.7%% compression, SHOULD WORK!)
echo 3 - Smart (may break structure, 99.7%% compression, risky)
echo 4 - Normal (small placeholders, 99.7%% compression, may not work)
echo 5 - Extreme (removes images, 99.9%% compression, will not work)
echo.
set /p choice="Enter choice (1-5): "

echo.
echo Optimizing %1...
echo.

if "%choice%"=="1" (
    powershell -ExecutionPolicy Bypass -File "optimize_safe.ps1" -InputFile "%1"
) else if "%choice%"=="2" (
    powershell -ExecutionPolicy Bypass -File "optimize_working.ps1" -InputFile "%1"
) else if "%choice%"=="3" (
    powershell -ExecutionPolicy Bypass -File "optimize_smart.ps1" -InputFile "%1"
) else if "%choice%"=="4" (
    powershell -ExecutionPolicy Bypass -File "optimize_lottie.ps1" -InputFile "%1"
) else if "%choice%"=="5" (
    powershell -ExecutionPolicy Bypass -File "optimize_extreme.ps1" -InputFile "%1"
) else (
    echo Invalid choice. Using Working mode...
    powershell -ExecutionPolicy Bypass -File "optimize_working.ps1" -InputFile "%1"
)

echo.
echo Done! Press any key to exit...
pause >nul
