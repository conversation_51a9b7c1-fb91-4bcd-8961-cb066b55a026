@echo off
echo Lottie JSON Optimizer
echo =====================
echo.

if "%~1"=="" (
    echo Usage: optimize.bat file.json [mode]
    echo.
    echo Modes:
    echo   safe   - Safe optimization (keeps images, 0.1%% compression)
    echo   smart  - Smart optimization (transparent pixels, 99.7%% compression)
    echo   normal - Normal optimization (small placeholders, 99.7%% compression)
    echo   extreme- Extreme optimization (removes images, 99.9%% compression)
    echo.
    echo Examples:
    echo   optimize.bat 1.json safe
    echo   optimize.bat 1.json smart
    echo   optimize.bat 1.json
    pause
    exit /b 1
)

if not exist "%~1" (
    echo File %~1 not found
    pause
    exit /b 1
)

set mode=%~2
if "%mode%"=="" set mode=smart

echo Optimizing %~1 with %mode% mode...
echo.

if "%mode%"=="safe" (
    powershell -ExecutionPolicy Bypass -File "optimize_safe.ps1" -InputFile "%~1"
) else if "%mode%"=="smart" (
    powershell -ExecutionPolicy Bypass -File "optimize_smart.ps1" -InputFile "%~1"
) else if "%mode%"=="normal" (
    powershell -ExecutionPolicy Bypass -File "optimize_lottie.ps1" -InputFile "%~1"
) else if "%mode%"=="extreme" (
    powershell -ExecutionPolicy Bypass -File "optimize_extreme.ps1" -InputFile "%~1"
) else (
    echo Unknown mode: %mode%
    echo Using smart mode instead...
    powershell -ExecutionPolicy Bypass -File "optimize_smart.ps1" -InputFile "%~1"
)

echo.
echo Done! Press any key to exit...
pause >nul
