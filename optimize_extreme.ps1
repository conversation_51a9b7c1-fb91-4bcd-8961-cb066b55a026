# Экстремальная оптимизация Lottie JSON - удаляет все изображения
param(
    [Parameter(Mandatory=$true)]
    [string]$InputFile,
    
    [Parameter(Mandatory=$false)]
    [string]$OutputFile = ""
)

function Optimize-LottieExtreme {
    param($InputFile, $OutputFile)
    
    # Проверяем существование файла
    if (-not (Test-Path $InputFile)) {
        Write-Host "❌ Файл $InputFile не найден" -ForegroundColor Red
        return $false
    }
    
    # Определяем выходной файл
    if ([string]::IsNullOrEmpty($OutputFile)) {
        $name = [System.IO.Path]::GetFileNameWithoutExtension($InputFile)
        $ext = [System.IO.Path]::GetExtension($InputFile)
        $dir = Split-Path $InputFile -Parent
        if ([string]::IsNullOrEmpty($dir)) { $dir = "." }
        $OutputFile = "$dir\$name`_extreme$ext"
    }
    
    Write-Host "🚀 ЭКСТРЕМАЛЬНАЯ оптимизация $InputFile..." -ForegroundColor Red
    
    try {
        # Читаем JSON
        $jsonContent = Get-Content $InputFile -Raw -Encoding UTF8
        $originalSize = (Get-Item $InputFile).Length
        Write-Host "📁 Исходный размер: $($originalSize.ToString('N0')) байт" -ForegroundColor Yellow
        
        $data = $jsonContent | ConvertFrom-Json
        
        $optimizations = @()
        
        # 1. ПОЛНОСТЬЮ удаляем все ассеты с изображениями
        $removedAssets = 0
        if ($data.assets) {
            $originalAssetCount = $data.assets.Count
            $data.assets = @()  # Полностью очищаем массив ассетов
            $removedAssets = $originalAssetCount
            $optimizations += "УДАЛЕНО $removedAssets изображений"
        }
        
        # 2. Удаляем метаданные
        $metaFields = @('meta', 'generator', 'description', 'author', 'keywords', 'comment', 'ddd')
        $removedMeta = @()
        foreach ($field in $metaFields) {
            if ($data.PSObject.Properties.Name -contains $field) {
                $data.PSObject.Properties.Remove($field)
                $removedMeta += $field
            }
        }
        if ($removedMeta.Count -gt 0) {
            $optimizations += "Удалены метаданные: $($removedMeta -join ', ')"
        }
        
        # 3. Агрессивно округляем числа
        function Round-Numbers($obj, $precision = 1) {
            if ($obj -is [System.Collections.IEnumerable] -and $obj -isnot [string]) {
                $result = @()
                foreach ($item in $obj) {
                    $result += Round-Numbers $item $precision
                }
                return $result
            } elseif ($obj -is [PSCustomObject]) {
                $newObj = New-Object PSCustomObject
                foreach ($prop in $obj.PSObject.Properties) {
                    $newObj | Add-Member -MemberType NoteProperty -Name $prop.Name -Value (Round-Numbers $prop.Value $precision)
                }
                return $newObj
            } elseif ($obj -is [double] -or $obj -is [float]) {
                return [Math]::Round($obj, $precision)
            } else {
                return $obj
            }
        }
        
        $data = Round-Numbers $data 1
        $optimizations += "Агрессивно округлены числа до 1 знака"
        
        # 4. Удаляем пустые слои и объекты
        function Remove-EmptyLayers($obj) {
            if ($obj -is [System.Collections.IEnumerable] -and $obj -isnot [string]) {
                $result = @()
                foreach ($item in $obj) {
                    $cleaned = Remove-EmptyLayers $item
                    if ($cleaned -ne $null) {
                        $result += $cleaned
                    }
                }
                return $result
            } elseif ($obj -is [PSCustomObject]) {
                $newObj = New-Object PSCustomObject
                foreach ($prop in $obj.PSObject.Properties) {
                    $cleaned = Remove-EmptyLayers $prop.Value
                    if ($cleaned -ne $null -and $cleaned -ne @() -and $cleaned -ne "") {
                        $newObj | Add-Member -MemberType NoteProperty -Name $prop.Name -Value $cleaned
                    }
                }
                if ($newObj.PSObject.Properties.Count -gt 0) {
                    return $newObj
                } else {
                    return $null
                }
            } else {
                return $obj
            }
        }
        
        $data = Remove-EmptyLayers $data
        $optimizations += "Удалены пустые слои и объекты"
        
        # 5. Сжимаем JSON максимально
        $compressedJson = $data | ConvertTo-Json -Compress
        
        # Сохраняем оптимизированный файл
        $compressedJson | Out-File -FilePath $OutputFile -Encoding UTF8 -NoNewline
        
        $optimizedSize = (Get-Item $OutputFile).Length
        $reduction = [math]::Round((($originalSize - $optimizedSize) / $originalSize) * 100, 1)
        
        Write-Host "✅ ЭКСТРЕМАЛЬНАЯ оптимизация завершена!" -ForegroundColor Green
        Write-Host "📁 Новый размер: $($optimizedSize.ToString('N0')) байт" -ForegroundColor Yellow
        Write-Host "📉 Сжатие: $reduction%" -ForegroundColor Green
        Write-Host "💾 Сохранено в: $OutputFile" -ForegroundColor Cyan
        Write-Host "🔧 Применено оптимизаций: $($optimizations.Count)" -ForegroundColor Magenta
        
        foreach ($opt in $optimizations) {
            Write-Host "   • $opt" -ForegroundColor Gray
        }
        
        Write-Host ""
        Write-Host "⚠️  ВНИМАНИЕ: Все изображения удалены! Анимация может не работать корректно." -ForegroundColor Yellow
        
        return $true
        
    } catch {
        Write-Host "❌ Ошибка: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Основная логика
if ([string]::IsNullOrEmpty($InputFile)) {
    Write-Host "Использование: .\optimize_extreme.ps1 -InputFile <input.json> [-OutputFile <output.json>]" -ForegroundColor Yellow
    Write-Host "Пример: .\optimize_extreme.ps1 -InputFile 1.json" -ForegroundColor Yellow
    exit 1
}

$result = Optimize-LottieExtreme -InputFile $InputFile -OutputFile $OutputFile

if ($result) {
    Write-Host "🎉 ЭКСТРЕМАЛЬНАЯ оптимизация завершена!" -ForegroundColor Green
} else {
    Write-Host "💥 Что-то пошло не так." -ForegroundColor Red
    exit 1
}
