@echo off
echo Lottie JSON Mass Optimizer
echo ==========================
echo.

if not exist "*.json" (
    echo No JSON files found in current directory
    pause
    exit /b 1
)

echo Found JSON files:
for %%f in (*.json) do (
    echo - %%f
)

echo.
echo Choose optimization level:
echo 1 - Normal optimization (safe, replaces images with placeholders)
echo 2 - Extreme optimization (removes all images, may break animation)
echo.
set /p choice="Enter choice (1 or 2): "

if "%choice%"=="1" (
    echo.
    echo Starting NORMAL optimization...
    echo.
    for %%f in (*.json) do (
        if not "%%f"=="%%~nf_optimized.json" if not "%%f"=="%%~nf_extreme.json" (
            echo Optimizing %%f...
            powershell -ExecutionPolicy Bypass -File "optimize_lottie.ps1" -InputFile "%%f"
            echo.
        )
    )
) else if "%choice%"=="2" (
    echo.
    echo Starting EXTREME optimization...
    echo WARNING: This will remove all images!
    echo.
    for %%f in (*.json) do (
        if not "%%f"=="%%~nf_optimized.json" if not "%%f"=="%%~nf_extreme.json" (
            echo Optimizing %%f...
            powershell -ExecutionPolicy Bypass -File "optimize_extreme.ps1" -InputFile "%%f"
            echo.
        )
    )
) else (
    echo Invalid choice. Exiting.
    pause
    exit /b 1
)

echo.
echo ================================
echo All files optimized successfully!
echo ================================
echo.
echo File sizes comparison:
for %%f in (*.json) do (
    powershell -Command "$size = (Get-Item '%%f').Length; Write-Host '%%f : ' -NoNewline; Write-Host $size.ToString('N0') -NoNewline; Write-Host ' bytes'"
)

echo.
echo Press any key to exit...
pause >nul
