# Безопасная оптимизация Lottie JSON - минимальные изменения
param(
    [Parameter(Mandatory=$true)]
    [string]$InputFile,
    
    [Parameter(Mandatory=$false)]
    [string]$OutputFile = ""
)

function Optimize-LottieSafe {
    param($InputFile, $OutputFile)
    
    # Проверяем существование файла
    if (-not (Test-Path $InputFile)) {
        Write-Host "❌ Файл $InputFile не найден" -ForegroundColor Red
        return $false
    }
    
    # Определяем выходной файл
    if ([string]::IsNullOrEmpty($OutputFile)) {
        $name = [System.IO.Path]::GetFileNameWithoutExtension($InputFile)
        $ext = [System.IO.Path]::GetExtension($InputFile)
        $dir = Split-Path $InputFile -Parent
        if ([string]::IsNullOrEmpty($dir)) { $dir = "." }
        $OutputFile = "$dir\$name`_safe$ext"
    }
    
    Write-Host "🛡️  БЕЗОПАСНАЯ оптимизация $InputFile..." -ForegroundColor Green
    
    try {
        # Читаем JSON
        $jsonContent = Get-Content $InputFile -Raw -Encoding UTF8
        $originalSize = (Get-Item $InputFile).Length
        Write-Host "📁 Исходный размер: $($originalSize.ToString('N0')) байт" -ForegroundColor Yellow
        
        $data = $jsonContent | ConvertFrom-Json
        
        $optimizations = @()
        
        # 1. Только удаляем метаданные (НЕ трогаем изображения!)
        $metaFields = @('meta', 'generator', 'description', 'author', 'keywords', 'comment')
        $removedMeta = @()
        foreach ($field in $metaFields) {
            if ($data.PSObject.Properties.Name -contains $field) {
                $data.PSObject.Properties.Remove($field)
                $removedMeta += $field
            }
        }
        if ($removedMeta.Count -gt 0) {
            $optimizations += "Удалены метаданные: $($removedMeta -join ', ')"
        }
        
        # 2. Очень осторожное округление чисел (только до 4 знаков)
        function Round-NumbersSafe($obj, $precision = 4) {
            if ($obj -is [System.Collections.IEnumerable] -and $obj -isnot [string]) {
                $result = @()
                foreach ($item in $obj) {
                    $result += Round-NumbersSafe $item $precision
                }
                return $result
            } elseif ($obj -is [PSCustomObject]) {
                $newObj = New-Object PSCustomObject
                foreach ($prop in $obj.PSObject.Properties) {
                    # Для критически важных свойств НЕ округляем вообще
                    if ($prop.Name -in @('w', 'h', 'fr', 'ip', 'op', 'st', 'ind')) {
                        $newObj | Add-Member -MemberType NoteProperty -Name $prop.Name -Value $prop.Value
                    } else {
                        $newObj | Add-Member -MemberType NoteProperty -Name $prop.Name -Value (Round-NumbersSafe $prop.Value $precision)
                    }
                }
                return $newObj
            } elseif ($obj -is [double] -or $obj -is [float]) {
                # Округляем только если число имеет больше знаков после запятой
                if ([Math]::Abs($obj - [Math]::Round($obj, $precision)) -gt 0.0001) {
                    return [Math]::Round($obj, $precision)
                } else {
                    return $obj
                }
            } else {
                return $obj
            }
        }
        
        $data = Round-NumbersSafe $data 4
        $optimizations += "Осторожно округлены числа (до 4 знаков, важные параметры не тронуты)"
        
        # 3. Сжимаем только JSON (убираем пробелы и отступы)
        $compressedJson = $data | ConvertTo-Json -Compress
        
        # Сохраняем оптимизированный файл
        $compressedJson | Out-File -FilePath $OutputFile -Encoding UTF8 -NoNewline
        
        $optimizedSize = (Get-Item $OutputFile).Length
        $reduction = [math]::Round((($originalSize - $optimizedSize) / $originalSize) * 100, 1)
        
        Write-Host "✅ БЕЗОПАСНАЯ оптимизация завершена!" -ForegroundColor Green
        Write-Host "📁 Новый размер: $($optimizedSize.ToString('N0')) байт" -ForegroundColor Yellow
        Write-Host "📉 Сжатие: $reduction%" -ForegroundColor Green
        Write-Host "💾 Сохранено в: $OutputFile" -ForegroundColor Cyan
        Write-Host "🔧 Применено оптимизаций: $($optimizations.Count)" -ForegroundColor Magenta
        
        foreach ($opt in $optimizations) {
            Write-Host "   • $opt" -ForegroundColor Gray
        }
        
        Write-Host ""
        Write-Host "✅ Изображения сохранены! Анимация будет работать на 100%!" -ForegroundColor Green
        
        return $true
        
    } catch {
        Write-Host "❌ Ошибка: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Основная логика
if ([string]::IsNullOrEmpty($InputFile)) {
    Write-Host "Использование: .\optimize_safe.ps1 -InputFile <input.json> [-OutputFile <output.json>]" -ForegroundColor Yellow
    Write-Host "Пример: .\optimize_safe.ps1 -InputFile 1.json" -ForegroundColor Yellow
    exit 1
}

$result = Optimize-LottieSafe -InputFile $InputFile -OutputFile $OutputFile

if ($result) {
    Write-Host "🎉 БЕЗОПАСНАЯ оптимизация завершена!" -ForegroundColor Green
} else {
    Write-Host "💥 Что-то пошло не так." -ForegroundColor Red
    exit 1
}
