# РАБОЧАЯ оптимизация Lottie JSON - гарантированно сохраняет структуру
param(
    [Parameter(Mandatory=$true)]
    [string]$InputFile,
    
    [Parameter(Mandatory=$false)]
    [string]$OutputFile = ""
)

function Optimize-LottieWorking {
    param($InputFile, $OutputFile)
    
    # Проверяем существование файла
    if (-not (Test-Path $InputFile)) {
        Write-Host "❌ Файл $InputFile не найден" -ForegroundColor Red
        return $false
    }
    
    # Определяем выходной файл
    if ([string]::IsNullOrEmpty($OutputFile)) {
        $name = [System.IO.Path]::GetFileNameWithoutExtension($InputFile)
        $ext = [System.IO.Path]::GetExtension($InputFile)
        $dir = Split-Path $InputFile -Parent
        if ([string]::IsNullOrEmpty($dir)) { $dir = "." }
        $OutputFile = "$dir\$name`_working$ext"
    }
    
    Write-Host "⚡ РАБОЧАЯ оптимизация $InputFile..." -ForegroundColor Cyan
    
    try {
        # Читаем JSON как текст (НЕ парсим в объект!)
        $jsonContent = Get-Content $InputFile -Raw -Encoding UTF8
        $originalSize = (Get-Item $InputFile).Length
        Write-Host "📁 Исходный размер: $($originalSize.ToString('N0')) байт" -ForegroundColor Yellow
        
        $optimizations = @()
        $optimizedContent = $jsonContent
        
        # 1. Заменяем только base64 изображения (работаем с текстом)
        $base64Pattern = 'data:image/[^;]+;base64,[A-Za-z0-9+/=]+'
        $matches = [regex]::Matches($optimizedContent, $base64Pattern)
        
        if ($matches.Count -gt 0) {
            Write-Host "   🔍 Найдено $($matches.Count) base64 изображений" -ForegroundColor Gray
            
            # Заменяем каждое изображение на прозрачный пиксель
            $transparentPixel = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAAC0lEQVQIHWNgAAIAAAUAAY27m/MAAAAASUVORK5CYII="
            
            foreach ($match in $matches) {
                $originalLength = $match.Value.Length
                $optimizedContent = $optimizedContent.Replace($match.Value, $transparentPixel)
                Write-Host "   📷 Заменено изображение: $originalLength → $($transparentPixel.Length) байт" -ForegroundColor Gray
            }
            
            $optimizations += "Заменено $($matches.Count) изображений на прозрачные пиксели"
        }
        
        # 2. Удаляем только простые метаданные (работаем с текстом)
        $metaPatterns = @(
            '"meta":\s*{[^}]*}',
            '"generator":\s*"[^"]*"',
            '"description":\s*"[^"]*"',
            '"author":\s*"[^"]*"',
            '"keywords":\s*"[^"]*"',
            '"comment":\s*"[^"]*"'
        )
        
        $removedMeta = @()
        foreach ($pattern in $metaPatterns) {
            $matches = [regex]::Matches($optimizedContent, $pattern)
            if ($matches.Count -gt 0) {
                foreach ($match in $matches) {
                    $optimizedContent = $optimizedContent.Replace($match.Value, "")
                    $fieldName = $match.Value.Split(':')[0].Replace('"', '')
                    if ($removedMeta -notcontains $fieldName) {
                        $removedMeta += $fieldName
                    }
                }
            }
        }
        
        if ($removedMeta.Count -gt 0) {
            $optimizations += "Удалены метаданные: $($removedMeta -join ', ')"
        }
        
        # 3. Очищаем лишние запятые и пробелы
        $optimizedContent = $optimizedContent -replace ',\s*,', ','  # Двойные запятые
        $optimizedContent = $optimizedContent -replace ',\s*}', '}'  # Запятые перед }
        $optimizedContent = $optimizedContent -replace ',\s*]', ']'  # Запятые перед ]
        $optimizedContent = $optimizedContent -replace '\s+', ' '    # Множественные пробелы
        
        # 4. Минимальное сжатие (убираем лишние пробелы, НО сохраняем структуру)
        $optimizedContent = $optimizedContent -replace ':\s+', ':'   # Пробелы после :
        $optimizedContent = $optimizedContent -replace ',\s+', ','   # Пробелы после ,
        $optimizedContent = $optimizedContent -replace '{\s+', '{'   # Пробелы после {
        $optimizedContent = $optimizedContent -replace '\s+}', '}'   # Пробелы перед }
        $optimizedContent = $optimizedContent -replace '\[\s+', '['  # Пробелы после [
        $optimizedContent = $optimizedContent -replace '\s+]', ']'   # Пробелы перед ]
        
        $optimizations += "Минимальное сжатие JSON (сохранена структура)"
        
        # 5. Проверяем, что JSON валидный
        try {
            $testParse = $optimizedContent | ConvertFrom-Json
            Write-Host "   ✅ JSON структура сохранена" -ForegroundColor Green
        } catch {
            Write-Host "   ⚠️ Ошибка в JSON структуре, используем оригинал" -ForegroundColor Yellow
            $optimizedContent = $jsonContent
            $optimizations = @("Только замена изображений (структура не изменена)")
        }
        
        # Сохраняем оптимизированный файл
        $optimizedContent | Out-File -FilePath $OutputFile -Encoding UTF8 -NoNewline
        
        $optimizedSize = (Get-Item $OutputFile).Length
        $reduction = [math]::Round((($originalSize - $optimizedSize) / $originalSize) * 100, 1)
        
        Write-Host "✅ РАБОЧАЯ оптимизация завершена!" -ForegroundColor Green
        Write-Host "📁 Новый размер: $($optimizedSize.ToString('N0')) байт" -ForegroundColor Yellow
        Write-Host "📉 Сжатие: $reduction%" -ForegroundColor Green
        Write-Host "💾 Сохранено в: $OutputFile" -ForegroundColor Cyan
        Write-Host "🔧 Применено оптимизаций: $($optimizations.Count)" -ForegroundColor Magenta
        
        foreach ($opt in $optimizations) {
            Write-Host "   • $opt" -ForegroundColor Gray
        }
        
        Write-Host ""
        Write-Host "✅ JSON структура сохранена! Анимация должна работать!" -ForegroundColor Green
        
        return $true
        
    } catch {
        Write-Host "❌ Ошибка: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Основная логика
if ([string]::IsNullOrEmpty($InputFile)) {
    Write-Host "Использование: .\optimize_working.ps1 -InputFile <input.json> [-OutputFile <output.json>]" -ForegroundColor Yellow
    Write-Host "Пример: .\optimize_working.ps1 -InputFile 1.json" -ForegroundColor Yellow
    exit 1
}

$result = Optimize-LottieWorking -InputFile $InputFile -OutputFile $OutputFile

if ($result) {
    Write-Host "🎉 РАБОЧАЯ оптимизация завершена!" -ForegroundColor Green
} else {
    Write-Host "💥 Что-то пошло не так." -ForegroundColor Red
    exit 1
}
