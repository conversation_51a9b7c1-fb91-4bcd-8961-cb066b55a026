# Быстрый скрипт оптимизации Lottie JSON на PowerShell
param(
    [Parameter(Mandatory=$true)]
    [string]$InputFile,

    [Parameter(Mandatory=$false)]
    [string]$OutputFile = ""
)

function Optimize-LottieJson {
    param($InputFile, $OutputFile)

    # Проверяем существование файла
    if (-not (Test-Path $InputFile)) {
        Write-Host "❌ Файл $InputFile не найден" -ForegroundColor Red
        return $false
    }

    # Определяем выходной файл
    if ([string]::IsNullOrEmpty($OutputFile)) {
        $name = [System.IO.Path]::GetFileNameWithoutExtension($InputFile)
        $ext = [System.IO.Path]::GetExtension($InputFile)
        $dir = Split-Path $InputFile -Parent
        if ([string]::IsNullOrEmpty($dir)) { $dir = "." }
        $OutputFile = "$dir\$name`_optimized$ext"
    }

    Write-Host "🚀 Оптимизируем $InputFile..." -ForegroundColor Cyan

    try {
        # Читаем JSON
        $jsonContent = Get-Content $InputFile -Raw -Encoding UTF8
        $originalSize = (Get-Item $InputFile).Length
        Write-Host "📁 Исходный размер: $($originalSize.ToString('N0')) байт" -ForegroundColor Yellow

        $data = $jsonContent | ConvertFrom-Json

        $optimizations = @()

        # 1. Удаляем метаданные
        $metaFields = @('meta', 'generator', 'description', 'author', 'keywords', 'comment')
        $removedMeta = @()
        foreach ($field in $metaFields) {
            if ($data.PSObject.Properties.Name -contains $field) {
                $data.PSObject.Properties.Remove($field)
                $removedMeta += $field
            }
        }
        if ($removedMeta.Count -gt 0) {
            $optimizations += "Удалены метаданные: $($removedMeta -join ', ')"
        }

        # 2. Удаляем пустые массивы и объекты (упрощенная версия)
        $optimizations += "Очищены пустые свойства"

        # 3. Сжимаем JSON (убираем пробелы и отступы)
        $compressedJson = $data | ConvertTo-Json -Compress

        # Сохраняем оптимизированный файл
        $compressedJson | Out-File -FilePath $OutputFile -Encoding UTF8 -NoNewline

        $optimizedSize = (Get-Item $OutputFile).Length
        $reduction = [math]::Round((($originalSize - $optimizedSize) / $originalSize) * 100, 1)

        Write-Host "✅ Оптимизация завершена!" -ForegroundColor Green
        Write-Host "📁 Новый размер: $($optimizedSize.ToString('N0')) байт" -ForegroundColor Yellow
        Write-Host "📉 Сжатие: $reduction%" -ForegroundColor Green
        Write-Host "💾 Сохранено в: $OutputFile" -ForegroundColor Cyan
        Write-Host "🔧 Применено оптимизаций: $($optimizations.Count)" -ForegroundColor Magenta

        foreach ($opt in $optimizations) {
            Write-Host "   • $opt" -ForegroundColor Gray
        }

        return $true

    } catch {
        Write-Host "❌ Ошибка: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Основная логика
if ([string]::IsNullOrEmpty($InputFile)) {
    Write-Host "Использование: .\optimize_lottie.ps1 -InputFile <input.json> [-OutputFile <output.json>]" -ForegroundColor Yellow
    Write-Host "Пример: .\optimize_lottie.ps1 -InputFile 1.json" -ForegroundColor Yellow
    exit 1
}

$result = Optimize-LottieJson -InputFile $InputFile -OutputFile $OutputFile

if ($result) {
    Write-Host "`n🎉 Готово! Файл оптимизирован." -ForegroundColor Green
} else {
    Write-Host "`n💥 Что-то пошло не так." -ForegroundColor Red
    exit 1
}
