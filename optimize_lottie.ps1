# Быстрый скрипт оптимизации Lottie JSON на PowerShell
param(
    [Parameter(Mandatory=$true)]
    [string]$InputFile,

    [Parameter(Mandatory=$false)]
    [string]$OutputFile = ""
)

function Optimize-LottieJson {
    param($InputFile, $OutputFile)

    # Проверяем существование файла
    if (-not (Test-Path $InputFile)) {
        Write-Host "❌ Файл $InputFile не найден" -ForegroundColor Red
        return $false
    }

    # Определяем выходной файл
    if ([string]::IsNullOrEmpty($OutputFile)) {
        $name = [System.IO.Path]::GetFileNameWithoutExtension($InputFile)
        $ext = [System.IO.Path]::GetExtension($InputFile)
        $dir = Split-Path $InputFile -Parent
        if ([string]::IsNullOrEmpty($dir)) { $dir = "." }
        $OutputFile = "$dir\$name`_optimized$ext"
    }

    Write-Host "🚀 Оптимизируем $InputFile..." -ForegroundColor Cyan

    try {
        # Читаем JSON
        $jsonContent = Get-Content $InputFile -Raw -Encoding UTF8
        $originalSize = (Get-Item $InputFile).Length
        Write-Host "📁 Исходный размер: $($originalSize.ToString('N0')) байт" -ForegroundColor Yellow

        $data = $jsonContent | ConvertFrom-Json

        $optimizations = @()

        # 1. Удаляем метаданные
        $metaFields = @('meta', 'generator', 'description', 'author', 'keywords', 'comment')
        $removedMeta = @()
        foreach ($field in $metaFields) {
            if ($data.PSObject.Properties.Name -contains $field) {
                $data.PSObject.Properties.Remove($field)
                $removedMeta += $field
            }
        }
        if ($removedMeta.Count -gt 0) {
            $optimizations += "Удалены метаданные: $($removedMeta -join ', ')"
        }

        # 2. Оптимизируем base64 изображения (основная оптимизация)
        $originalAssetCount = 0
        $optimizedAssetCount = 0
        if ($data.assets) {
            $originalAssetCount = $data.assets.Count
            $newAssets = @()
            foreach ($asset in $data.assets) {
                if ($asset.p -and $asset.p.StartsWith("data:image/")) {
                    # Заменяем на заглушку или удаляем
                    $asset.p = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
                    $optimizations += "Заменено изображение $($asset.id) на заглушку"
                }
                $newAssets += $asset
            }
            $data.assets = $newAssets
            $optimizedAssetCount = $newAssets.Count
        }

        # 3. Округляем числа для уменьшения размера
        function Round-Numbers($obj, $precision = 2) {
            if ($obj -is [System.Collections.IEnumerable] -and $obj -isnot [string]) {
                $result = @()
                foreach ($item in $obj) {
                    $result += Round-Numbers $item $precision
                }
                return $result
            } elseif ($obj -is [PSCustomObject]) {
                $newObj = New-Object PSCustomObject
                foreach ($prop in $obj.PSObject.Properties) {
                    $newObj | Add-Member -MemberType NoteProperty -Name $prop.Name -Value (Round-Numbers $prop.Value $precision)
                }
                return $newObj
            } elseif ($obj -is [double] -or $obj -is [float]) {
                return [Math]::Round($obj, $precision)
            } else {
                return $obj
            }
        }

        $data = Round-Numbers $data 2
        $optimizations += "Округлены числа до 2 знаков"

        # 3. Сжимаем JSON (убираем пробелы и отступы)
        $compressedJson = $data | ConvertTo-Json -Compress

        # Сохраняем оптимизированный файл
        $compressedJson | Out-File -FilePath $OutputFile -Encoding UTF8 -NoNewline

        $optimizedSize = (Get-Item $OutputFile).Length
        $reduction = [math]::Round((($originalSize - $optimizedSize) / $originalSize) * 100, 1)

        Write-Host "✅ Оптимизация завершена!" -ForegroundColor Green
        Write-Host "📁 Новый размер: $($optimizedSize.ToString('N0')) байт" -ForegroundColor Yellow
        Write-Host "📉 Сжатие: $reduction%" -ForegroundColor Green
        Write-Host "💾 Сохранено в: $OutputFile" -ForegroundColor Cyan
        Write-Host "🔧 Применено оптимизаций: $($optimizations.Count)" -ForegroundColor Magenta

        foreach ($opt in $optimizations) {
            Write-Host "   • $opt" -ForegroundColor Gray
        }

        return $true

    } catch {
        Write-Host "❌ Ошибка: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Основная логика
if ([string]::IsNullOrEmpty($InputFile)) {
    Write-Host "Использование: .\optimize_lottie.ps1 -InputFile <input.json> [-OutputFile <output.json>]" -ForegroundColor Yellow
    Write-Host "Пример: .\optimize_lottie.ps1 -InputFile 1.json" -ForegroundColor Yellow
    exit 1
}

$result = Optimize-LottieJson -InputFile $InputFile -OutputFile $OutputFile

if ($result) {
    Write-Host "`n🎉 Готово! Файл оптимизирован." -ForegroundColor Green
} else {
    Write-Host "`n💥 Что-то пошло не так." -ForegroundColor Red
    exit 1
}
