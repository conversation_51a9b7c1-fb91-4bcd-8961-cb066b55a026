LOTTIE JSON OPTIMIZER - Быстрая оптимизация без заморочек
===========================================================

РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:
- Исходный файл: 3,536,750 байт (3.5 МБ)
- Обычная оптимизация: 9,741 байт (99.7% сжатие)
- Экстремальная оптимизация: 4,415 байт (99.9% сжатие)

КАК ИСПОЛЬЗОВАТЬ:
================

1. ПРОСТОЙ СПОСОБ (batch файл):
   optimize.bat 1.json

2. ОБЫЧНАЯ ОПТИМИЗАЦИЯ (PowerShell):
   .\optimize_lottie.ps1 -InputFile 1.json

3. ЭКСТРЕМАЛЬНАЯ ОПТИМИЗАЦИЯ (PowerShell):
   .\optimize_extreme.ps1 -InputFile 1.json

ЧТО ДЕЛАЮТ СКРИПТЫ:
==================

ОБЫЧНАЯ ОПТИМИЗАЦИЯ (optimize_lottie.ps1):
- Заменяет base64 изображения на маленькие заглушки
- Удаляет метаданные
- Округляет числа до 2 знаков
- Сжимает JSON (убирает пробелы)

ЭКСТРЕМАЛЬНАЯ ОПТИМИЗАЦИЯ (optimize_extreme.ps1):
- ПОЛНОСТЬЮ удаляет все изображения
- Удаляет метаданные
- Агрессивно округляет числа до 1 знака
- Удаляет пустые слои и объекты
- Максимально сжимает JSON

ВНИМАНИЕ:
=========
- Экстремальная оптимизация может сломать анимацию!
- Используйте обычную оптимизацию для рабочих файлов
- Экстремальную - только для тестов размера

ФАЙЛЫ В ПРОЕКТЕ:
===============
- optimize.bat - Простой запуск
- optimize_lottie.ps1 - Основной скрипт (безопасный)
- optimize_extreme.ps1 - Экстремальная оптимизация
- optimize_lottie.py - Python версия (нужен Python)

ПРИМЕР ИСПОЛЬЗОВАНИЯ:
====================
1. Скопируйте ваш JSON файл в эту папку
2. Запустите: optimize.bat ваш_файл.json
3. Получите оптимизированный файл с суффиксом _optimized

Готово! 🎉
