LOTTIE JSON OPTIMIZER - Быстрая оптимизация без заморочек
===========================================================

РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:
- Исходный файл: 3,536,750 байт (3.5 МБ)
- Безопасная оптимизация: 3,534,467 байт (0.1% сжатие) ✅ РАБОТАЕТ 100%
- Умная оптимизация: 9,300 байт (99.7% сжатие) ✅ ДОЛЖНА РАБОТАТЬ
- Обычная оптимизация: 9,741 байт (99.7% сжатие) ⚠️ МОЖЕТ НЕ РАБОТАТЬ
- Экстремальная оптимизация: 4,415 байт (99.9% сжатие) ❌ НЕ РАБОТАЕТ

КАК ИСПОЛЬЗОВАТЬ:
================

1. ПРОСТОЙ СПОСОБ (batch файл):
   optimize.bat 1.json safe      - Безопасная (сохраняет изображения)
   optimize.bat 1.json smart     - Умная (прозрачные пиксели)
   optimize.bat 1.json normal    - Обычная (маленькие заглушки)
   optimize.bat 1.json extreme   - Экстремальная (удаляет изображения)

2. ПРЯМОЙ ВЫЗОВ (PowerShell):
   .\optimize_safe.ps1 -InputFile 1.json      - Безопасная
   .\optimize_smart.ps1 -InputFile 1.json     - Умная
   .\optimize_lottie.ps1 -InputFile 1.json    - Обычная
   .\optimize_extreme.ps1 -InputFile 1.json   - Экстремальная

ЧТО ДЕЛАЮТ СКРИПТЫ:
==================

БЕЗОПАСНАЯ ОПТИМИЗАЦИЯ (optimize_safe.ps1):
- Сохраняет ВСЕ изображения без изменений
- Удаляет только метаданные
- Очень осторожно округляет числа (до 4 знаков)
- Сжимает только JSON (убирает пробелы)
- ✅ ГАРАНТИРОВАННО РАБОТАЕТ

УМНАЯ ОПТИМИЗАЦИЯ (optimize_smart.ps1):
- Заменяет изображения на прозрачные пиксели (сохраняет структуру)
- Удаляет метаданные
- Умно округляет числа (важные параметры не трогает)
- Сохраняет важные свойства анимации
- ✅ ДОЛЖНА РАБОТАТЬ

ОБЫЧНАЯ ОПТИМИЗАЦИЯ (optimize_lottie.ps1):
- Заменяет base64 изображения на маленькие заглушки
- Удаляет метаданные
- Округляет числа до 2 знаков
- Сжимает JSON (убирает пробелы)
- ⚠️ МОЖЕТ НЕ РАБОТАТЬ

ЭКСТРЕМАЛЬНАЯ ОПТИМИЗАЦИЯ (optimize_extreme.ps1):
- ПОЛНОСТЬЮ удаляет все изображения
- Удаляет метаданные
- Агрессивно округляет числа до 1 знака
- Удаляет пустые слои и объекты
- Максимально сжимает JSON
- ❌ НЕ РАБОТАЕТ

РЕКОМЕНДАЦИИ:
=============
✅ Для РАБОЧИХ файлов используйте БЕЗОПАСНУЮ или УМНУЮ оптимизацию
⚠️ Обычную оптимизацию тестируйте перед использованием
❌ Экстремальную используйте только для тестов размера

ФАЙЛЫ В ПРОЕКТЕ:
===============
- optimize.bat - Главный скрипт с выбором режима
- optimize_all.bat - Массовая обработка всех JSON файлов
- optimize_safe.ps1 - Безопасная оптимизация (100% работает)
- optimize_smart.ps1 - Умная оптимизация (должна работать)
- optimize_lottie.ps1 - Обычная оптимизация (может не работать)
- optimize_extreme.ps1 - Экстремальная оптимизация (не работает)

БЫСТРЫЙ СТАРТ:
=============
1. Скопируйте ваш JSON файл в эту папку
2. Запустите: .\optimize_simple.bat ваш_файл.json
3. Выберите режим оптимизации (рекомендуется 2 - Smart)
4. Получите оптимизированный файл
5. Проверьте, что анимация работает
6. Если не работает - используйте режим 1 (Safe)

Готово! 🎉
