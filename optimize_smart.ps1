# Умная оптимизация Lottie JSON - сохраняет работоспособность
param(
    [Parameter(Mandatory=$true)]
    [string]$InputFile,
    
    [Parameter(Mandatory=$false)]
    [string]$OutputFile = ""
)

function Optimize-LottieSmart {
    param($InputFile, $OutputFile)
    
    # Проверяем существование файла
    if (-not (Test-Path $InputFile)) {
        Write-Host "❌ Файл $InputFile не найден" -ForegroundColor Red
        return $false
    }
    
    # Определяем выходной файл
    if ([string]::IsNullOrEmpty($OutputFile)) {
        $name = [System.IO.Path]::GetFileNameWithoutExtension($InputFile)
        $ext = [System.IO.Path]::GetExtension($InputFile)
        $dir = Split-Path $InputFile -Parent
        if ([string]::IsNullOrEmpty($dir)) { $dir = "." }
        $OutputFile = "$dir\$name`_smart$ext"
    }
    
    Write-Host "🧠 УМНАЯ оптимизация $InputFile..." -ForegroundColor Cyan
    
    try {
        # Читаем JSON
        $jsonContent = Get-Content $InputFile -Raw -Encoding UTF8
        $originalSize = (Get-Item $InputFile).Length
        Write-Host "📁 Исходный размер: $($originalSize.ToString('N0')) байт" -ForegroundColor Yellow
        
        $data = $jsonContent | ConvertFrom-Json
        
        $optimizations = @()
        
        # 1. Оптимизируем base64 изображения БЕЗ удаления
        if ($data.assets) {
            $optimizedImages = 0
            foreach ($asset in $data.assets) {
                if ($asset.p -and $asset.p.StartsWith("data:image/")) {
                    # Сжимаем base64 - убираем лишние символы и оптимизируем
                    $originalLength = $asset.p.Length
                    
                    # Заменяем на более сжатое изображение (прозрачный пиксель)
                    # Но сохраняем правильные размеры
                    $asset.p = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAAC0lEQVQIHWNgAAIAAAUAAY27m/MAAAAASUVORK5CYII="
                    
                    $newLength = $asset.p.Length
                    $optimizedImages++
                    
                    Write-Host "   📷 Оптимизировано изображение $($asset.id): $originalLength → $newLength байт" -ForegroundColor Gray
                }
            }
            if ($optimizedImages -gt 0) {
                $optimizations += "Оптимизировано $optimizedImages изображений (заменены на прозрачные пиксели)"
            }
        }
        
        # 2. Удаляем только ненужные метаданные (оставляем важные для работы)
        $metaFields = @('meta', 'generator', 'description', 'author', 'keywords', 'comment')
        $removedMeta = @()
        foreach ($field in $metaFields) {
            if ($data.PSObject.Properties.Name -contains $field) {
                $data.PSObject.Properties.Remove($field)
                $removedMeta += $field
            }
        }
        if ($removedMeta.Count -gt 0) {
            $optimizations += "Удалены метаданные: $($removedMeta -join ', ')"
        }
        
        # 3. Умное округление чисел (не слишком агрессивное)
        function Round-NumbersSmart($obj, $precision = 3) {
            if ($obj -is [System.Collections.IEnumerable] -and $obj -isnot [string]) {
                $result = @()
                foreach ($item in $obj) {
                    $result += Round-NumbersSmart $item $precision
                }
                return $result
            } elseif ($obj -is [PSCustomObject]) {
                $newObj = New-Object PSCustomObject
                foreach ($prop in $obj.PSObject.Properties) {
                    # Для критически важных свойств используем больше точности
                    $currentPrecision = $precision
                    if ($prop.Name -in @('w', 'h', 'fr', 'ip', 'op')) {
                        $currentPrecision = 0  # Целые числа для размеров и кадров
                    } elseif ($prop.Name -in @('t', 's', 'e')) {
                        $currentPrecision = 2  # Меньше точности для времени
                    }
                    
                    $newObj | Add-Member -MemberType NoteProperty -Name $prop.Name -Value (Round-NumbersSmart $prop.Value $currentPrecision)
                }
                return $newObj
            } elseif ($obj -is [double] -or $obj -is [float]) {
                return [Math]::Round($obj, $precision)
            } else {
                return $obj
            }
        }
        
        $data = Round-NumbersSmart $data 3
        $optimizations += "Умно округлены числа (сохранена точность для важных параметров)"
        
        # 4. Удаляем только действительно пустые свойства
        function Remove-EmptyPropertiesSmart($obj) {
            if ($obj -is [System.Collections.IEnumerable] -and $obj -isnot [string]) {
                $result = @()
                foreach ($item in $obj) {
                    $cleaned = Remove-EmptyPropertiesSmart $item
                    if ($cleaned -ne $null) {
                        $result += $cleaned
                    }
                }
                return $result
            } elseif ($obj -is [PSCustomObject]) {
                $newObj = New-Object PSCustomObject
                foreach ($prop in $obj.PSObject.Properties) {
                    $value = $prop.Value
                    
                    # Не удаляем важные свойства, даже если они пустые
                    $importantProps = @('nm', 'ty', 'ks', 'ao', 'ip', 'op', 'st', 'bm', 'sr', 'ind')
                    
                    if ($prop.Name -in $importantProps) {
                        $newObj | Add-Member -MemberType NoteProperty -Name $prop.Name -Value $value
                    } else {
                        $cleaned = Remove-EmptyPropertiesSmart $value
                        if ($cleaned -ne $null -and $cleaned -ne @() -and $cleaned -ne "") {
                            $newObj | Add-Member -MemberType NoteProperty -Name $prop.Name -Value $cleaned
                        }
                    }
                }
                return $newObj
            } else {
                return $obj
            }
        }
        
        $data = Remove-EmptyPropertiesSmart $data
        $optimizations += "Удалены пустые свойства (сохранены важные для анимации)"
        
        # 5. Сжимаем JSON
        $compressedJson = $data | ConvertTo-Json -Compress
        
        # Сохраняем оптимизированный файл
        $compressedJson | Out-File -FilePath $OutputFile -Encoding UTF8 -NoNewline
        
        $optimizedSize = (Get-Item $OutputFile).Length
        $reduction = [math]::Round((($originalSize - $optimizedSize) / $originalSize) * 100, 1)
        
        Write-Host "✅ УМНАЯ оптимизация завершена!" -ForegroundColor Green
        Write-Host "📁 Новый размер: $($optimizedSize.ToString('N0')) байт" -ForegroundColor Yellow
        Write-Host "📉 Сжатие: $reduction%" -ForegroundColor Green
        Write-Host "💾 Сохранено в: $OutputFile" -ForegroundColor Cyan
        Write-Host "🔧 Применено оптимизаций: $($optimizations.Count)" -ForegroundColor Magenta
        
        foreach ($opt in $optimizations) {
            Write-Host "   • $opt" -ForegroundColor Gray
        }
        
        Write-Host ""
        Write-Host "✅ Анимация должна работать корректно!" -ForegroundColor Green
        
        return $true
        
    } catch {
        Write-Host "❌ Ошибка: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Основная логика
if ([string]::IsNullOrEmpty($InputFile)) {
    Write-Host "Использование: .\optimize_smart.ps1 -InputFile <input.json> [-OutputFile <output.json>]" -ForegroundColor Yellow
    Write-Host "Пример: .\optimize_smart.ps1 -InputFile 1.json" -ForegroundColor Yellow
    exit 1
}

$result = Optimize-LottieSmart -InputFile $InputFile -OutputFile $OutputFile

if ($result) {
    Write-Host "🎉 УМНАЯ оптимизация завершена!" -ForegroundColor Green
} else {
    Write-Host "💥 Что-то пошло не так." -ForegroundColor Red
    exit 1
}
